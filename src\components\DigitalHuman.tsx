import { useEffect, useState, useCallback, useRef } from 'react';
import { v4 as uuidV4 } from 'uuid';
import DHIframe from '@bddh/starling-dhiframe';
import { ConfigService } from '../services/configService';
import { DigitalHumanConfig } from '../types/config';
import { UploadService } from '../services/uploadService';
import { API_BASE_URL } from '../config/constants';
import { AliASRRecognition } from '../services/aliASRRecognition';
import '../App.css';
import micOnIcon from '../assets/icons/mic-on.svg';
import micOffIcon from '../assets/icons/mic-off.svg';

interface Message {
  type: 'user' | 'digitalHuman';
  content: string;
  timestamp: number;
  isStreaming?: boolean; // 标记是否为流式响应中的消息
}

const DigitalHuman = () => {
  const [realTimeVideoReady, setRealTimeVideoReady] = useState(false);
  const [wsConnected, setWsConnected] = useState(false);
  const [videoIsMuted, setVideoIsMuted] = useState(false);
  const [commandId] = useState(uuidV4());
  const [checkOver, setCheckOver] = useState(false);
  const [isRecording, setIsRecording] = useState(false);
  const [recognition, setRecognition] = useState<any>(null);
  const [messages, setMessages] = useState<Message[]>([]);
  const [isProcessing, setIsProcessing] = useState(false);
  const [inputText, setInputText] = useState('');
  const [isDigitalHumanSpeaking, setIsDigitalHumanSpeaking] = useState(false);
  // 添加数字人渲染状态追踪变量
  const [isRenderingStarted, setIsRenderingStarted] = useState(false);
  // 添加 ref 来跟踪数字人说话状态，确保能获取到最新值
  const isDigitalHumanSpeakingRef = useRef(false);
  const [currentRenderRequestId, setCurrentRenderRequestId] = useState<string>('');
  const [config, setConfig] = useState<DigitalHumanConfig>({} as DigitalHumanConfig);
  const configRef = useRef<DigitalHumanConfig>({} as DigitalHumanConfig);
  const [currentBackground, setCurrentBackground] = useState<string>('');
  const [currentBackgroundType, setCurrentBackgroundType] = useState<'image' | 'video'>('image');
  const [overlayOpacity, setOverlayOpacity] = useState(0);
  const [displayContent, setDisplayContent] = useState<string>('数字人助手正在加载中...');
  const chatContainerRef = useRef<HTMLDivElement>(null);
  const [iframeStyle, setIframeStyle] = useState({
    width: '450px',
    height: '800px'
  });
  const [videoLoaded, setVideoLoaded] = useState(false);
  const videoRef = useRef<HTMLVideoElement>(null);
  const [isWakeupActive, setIsWakeupActive] = useState(false);
  const silenceStartTimeRef = useRef<number>(Date.now());
  // 添加语音识别中间结果状态变量
  const [interimText, setInterimText] = useState<string>('');
  
  /**
   * 语音识别优化说明：
   * 1. 增加超时时间：将AliASRRecognition的resultTimeoutMs和silenceTimeoutMs从1500ms增加到4000ms
   *    这样可以允许用户在说话时有更长的停顿时间，避免过早触发最终结果
   * 
   * 2. 软终止处理：即使收到isFinal=true的结果，也不立即处理，而是设置一个定时器
   *    在2秒内如果又有新的识别结果，则取消之前的定时器，重新开始计时
   *    这样可以有效解决用户说话中间停顿导致的句子被截断问题
   * 
   * 3. 后端VAD参数：需要修改AliASRRecognition类以支持在start方法中传递参数
   *    目前暂未实现，可以在AliASRRecognition类中添加支持
   */
  
  // 添加软终止相关状态变量
  const [pendingFinalText, setPendingFinalText] = useState<string>('');
  const finalTimerRef = useRef<NodeJS.Timeout | null>(null);
  const softTerminationEnabledRef = useRef<boolean>(true);
  const softTerminationDelayRef = useRef<number>(2000); // 软终止延迟时间，默认2秒

  // 轮播相关状态
  const [carouselActive, setCarouselActive] = useState(false);
  const [carouselCurrentIndex, setCarouselCurrentIndex] = useState(0);
  const [carouselPaused, setCarouselPaused] = useState(false);
  const carouselTimerRef = useRef<NodeJS.Timeout | null>(null);
  const carouselItemsRef = useRef<any[]>([]);
  const [currentCarouselItemType, setCurrentCarouselItemType] = useState<'image' | 'video'>('image');

  // 关键词触发状态跟踪
  const [keywordTriggered, setKeywordTriggered] = useState(false);
  const keywordResetTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  // 添加新的状态管理变量
  const recognitionStateRef = useRef({
    isStarting: false,
    isStopping: false,
    isActive: false,
    retryCount: 0,
    lastError: null as string | null,
    lastStartTime: 0,
    cooldownEndTime: 0,
    isProcessing: false
  });

  const maxRetries = 10;
  const retryDelay = 3000;
  const minStartInterval = 1000;
  const cooldownPeriod = 2000;

  const dhIframe = new DHIframe('digital-human-iframe');

  // 添加音频状态管理
  const [audioState, setAudioState] = useState({
    isMuted: true,
    hasUserInteracted: false,
    isInitialized: false
  });

  // 添加语音识别状态管理
  const [recognitionState, setRecognitionState] = useState({
    isConnecting: false,
    isConnected: false,
    hasError: false,
    errorMessage: '',
    reconnecting: false,
    reconnectCount: 0
  });

  // 修改唤醒状态管理
  const wakeupStateRef = useRef({
    isActive: false,
    lastUpdateTime: 0,
    timeoutId: null as NodeJS.Timeout | null
  });

  // 添加引用跟踪欢迎消息状态
  const hasShownWelcomeRef = useRef(false);

  // 添加打断关键词配置
  const defaultInterruptionKeywords = ['停止', '等一下', '打断', '停下', '先等等', '暂停'];

  // 添加配置加载状态
  const [configLoading, setConfigLoading] = useState(true);

  // 添加手动启动状态管理
  const [hasManuallyStarted, setHasManuallyStarted] = useState(false);

  // 视频声音控制
  const [videoSoundEnabled, setVideoSoundEnabled] = useState(false);

  // 添加 videoSoundEnabled 的 ref，确保在异步操作中能获取到最新状态
  const videoSoundEnabledRef = useRef(false);

  // 同步 videoSoundEnabled 状态到 ref
  useEffect(() => {
    videoSoundEnabledRef.current = videoSoundEnabled;
    console.log('videoSoundEnabled 状态更新:', videoSoundEnabled);
  }, [videoSoundEnabled]);

  // 同步 isDigitalHumanSpeaking 状态到 ref
  useEffect(() => {
    isDigitalHumanSpeakingRef.current = isDigitalHumanSpeaking;
    console.log('isDigitalHumanSpeaking 状态更新:', isDigitalHumanSpeaking);
  }, [isDigitalHumanSpeaking]);

  // 根据URL后缀判断资源类型 - 移到组件顶部
  const getResourceType = (url: string): 'image' | 'video' => {
    const videoExtensions = ['.mp4', '.mov', '.avi', '.wmv', '.flv', '.webm'];
    const extension = url.toLowerCase().substring(url.lastIndexOf('.'));
    return videoExtensions.includes(extension) ? 'video' : 'image';
  };

  // 计算实际位置 - 移到组件顶部
  const calculateActualPosition = (position: { x: number; y: number }) => {
    const containerWidth = window.innerWidth;
    const containerHeight = window.innerHeight;
    return {
      x: (position.x / 100) * containerWidth,
      y: (position.y / 100) * containerHeight
    };
  };

  // 修改背景切换逻辑 - 移到组件顶部
  const changeBackground = (newBackground: string, newType: 'image' | 'video', keepSound: boolean = false) => {
    if (newBackground === currentBackground) return;

    // 背景变更时重置视频声音状态，除非指定保持声音状态
    if (!keepSound) {
      setVideoSoundEnabled(false);
    }

    // 先淡入黑色遮罩
    setOverlayOpacity(1);

    // 等待遮罩完全显示后切换背景
    setTimeout(() => {
      setCurrentBackground(newBackground);
      setCurrentBackgroundType(newType);

      // 淡出黑色遮罩
      setOverlayOpacity(0);

      // 等待遮罩完全消失后重置状态
      setTimeout(() => {
      }, 500);
    }, 500);
  };

  useEffect(() => {
    loadConfig();
  }, []);

  // 获取唤醒提示文本
  const getWakeupPrompt = useCallback(() => {
    const currentConfig = configRef.current;
    const wakeupPhrases = currentConfig.voiceWakeupPhrases || [];

    if (wakeupPhrases.length === 0) {
      return '请说唤醒词';
    }

    if (wakeupPhrases.length === 1) {
      return `请说"${wakeupPhrases[0]}"唤醒我`;
    }

    // 多个唤醒词的情况
    const phrasesList = wakeupPhrases.map(phrase => `"${phrase}"`).join(' 或 ');
    return `请说${phrasesList}唤醒我`;
  }, []);

  // 添加视频声音状态监听
  useEffect(() => {
    if (videoSoundEnabled) {
      // 获取当前配置的打断关键词
      const currentConfig = configRef.current;
      const interruptionKeywords = currentConfig.interruptionKeywords?.length ?
        currentConfig.interruptionKeywords.join('、') : '停止、等一下、打断';

      // 视频播放时显示打断提示
      setDisplayContent(`视频播放中...请说"${interruptionKeywords}"等关键词来打断播放`);
    }
  }, [videoSoundEnabled]);
  
  // 修改 useEffect 中的显示内容设置
  useEffect(() => {
    // 如果视频声音已启用，不更新显示内容
    if (videoSoundEnabled) {
      return;
    }

    // 数字人正在渲染，显示打断提示
    if (isRenderingStarted) {
      const currentConfig = configRef.current;
      const interruptionKeywords = currentConfig.interruptionKeywords?.length ?
        currentConfig.interruptionKeywords.join('、') : '停止、等一下、打断';
      
      setDisplayContent(`数字人正在说话，可以说"${interruptionKeywords}"来打断`);
      return;
    }

    if (!hasManuallyStarted) {
      setDisplayContent('点击麦克风按钮开启语音对话');
    } else if (config.voiceWakeupEnabled && !wakeupStateRef.current.isActive) {
      // 使用wakeupStateRef.current.isActive替代isWakeupActive
      console.log('更新显示内容为唤醒提示，当前唤醒状态:', {
        isWakeupActive,
        wakeupRefIsActive: wakeupStateRef.current.isActive
      });
      setDisplayContent(getWakeupPrompt());
    }
  }, [config.voiceWakeupEnabled, hasManuallyStarted, isWakeupActive, getWakeupPrompt, videoSoundEnabled, isRenderingStarted]);

  const loadConfig = async () => {
    try {
      setConfigLoading(true);
      const configService = ConfigService.getInstance();
      const serverConfig = await configService.getConfig();
      console.log('从服务器加载的配置:', serverConfig);

      // 直接使用服务器配置，不再合并默认配置
      if (!serverConfig) {
        throw new Error('服务器配置加载失败');
      }

      // 将服务器配置设置为当前配置
      configRef.current = serverConfig;
      setConfig(serverConfig);

      // 确保新属性有默认值
      if (serverConfig.videoSoundEnabled === undefined) {
        configRef.current.videoSoundEnabled = false;
      }

      if (!serverConfig.videoPlayPhrases || !serverConfig.videoPlayPhrases.length) {
        configRef.current.videoPlayPhrases = ['播放视频'];
      }

      if (serverConfig.keywordDisplayDuration === undefined) {
        configRef.current.keywordDisplayDuration = 5; // 默认5秒
      }

      // 设置相似度阈值默认值
      if (serverConfig.similarityThreshold === undefined) {
        configRef.current.similarityThreshold = 0.4; // 默认设置为0.4，更适合中文场景
      }
      
      // 设置软终止功能配置 - 使用类型断言避免TypeScript错误
      if ((serverConfig as any).softTerminationEnabled !== undefined) {
        softTerminationEnabledRef.current = (serverConfig as any).softTerminationEnabled;
      }
      
      if ((serverConfig as any).softTerminationDelay !== undefined) {
        softTerminationDelayRef.current = (serverConfig as any).softTerminationDelay;
      }

      // 检查必要的配置
      if (!serverConfig.openaiApiKey) {
        console.warn('未配置 AI API 密钥，请在配置页面设置');
        setDisplayContent('请在配置页面设置 AI API 密钥');
      }

      // 如果启用了轮播，延迟启动轮播（等待组件完全初始化）
      if (serverConfig.carouselEnabled) {
        console.log('轮播已启用，延迟启动轮播');
        setTimeout(() => {
          startCarousel();
        }, 2000);
      }

      setConfigLoading(false);
    } catch (err) {
      console.error('加载配置失败:', err);
      setDisplayContent('加载配置失败，请稍后重试');
      setConfigLoading(false);
    }
  };

  // 修改 callOpenAI 函数以提供更友好的错误提示
  const callOpenAI = async (text: string) => {
    try {
      // 将历史消息转换为标准消息格式，只保留最近的10条对话
      const historyMessages = messages
        .slice(-10)
        .map(msg => ({
          role: msg.type === 'user' ? 'user' : 'assistant',
          content: msg.content
        }));

      // 如果使用流式响应，先删除之前自动添加的临时消息，这样整个流程由这个函数控制
      setMessages(prev => {
        // 检查是否有以空字符串开头的数字人消息(临时消息)，有则删除
        if (prev.length > 0 && prev[prev.length - 1].type === 'digitalHuman' && prev[prev.length - 1].content === '') {
          return prev.slice(0, -1);
        }
        return prev;
      });

      // 创建临时变量存储生成中的回复，用于关键词匹配
      let tempResponse = '';
      let finalResponse = '';

      // 创建一个临时消息ID，用于标识当前流式响应
      const tempMessageId = Date.now();

      // 创建一个临时消息，用于显示流式生成效果
      setMessages(prev => [...prev, {
        type: 'digitalHuman' as const,
        content: '',
        timestamp: tempMessageId,
        isStreaming: true // 添加标记表示这是流式响应中的消息
      }]);

      // 调用后端AI流式代理接口
      const response = await fetch(`${API_BASE_URL}/ai/stream-chat`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          messages: historyMessages,
          userInput: text
        })
      });

      if (!response.ok) {
        throw new Error(`请求失败: ${response.status}`);
      }

      // 处理SSE流式响应
      const reader = response.body?.getReader();
      const decoder = new TextDecoder();

      if (!reader) {
        throw new Error('无法获取响应流');
      }

      // 处理流式响应
      while (true) {
        const { done, value } = await reader.read();
        if (done) {
          break;
        }

        // 解码二进制数据为文本
        const chunk = decoder.decode(value, { stream: true });

        // 解析数据块
        const lines = chunk
          .split('\n\n')
          .filter(line => line.trim() !== '' && line.trim() !== 'data: [DONE]');

        for (const line of lines) {
          // 确保行是以data:开头的
          if (line.startsWith('data:')) {
            try {
              const jsonData = line.replace(/^data: /, '').trim();
              if (jsonData) {
                const data = JSON.parse(jsonData);

                // 提取生成的内容
                if (data.choices && data.choices[0].message && data.choices[0].message.content) {
                  const content = data.choices[0].message.content;
                  tempResponse = content;

                  // 实时更新临时消息，通过timestamp(tempMessageId)确保找到正确的消息
                  setMessages(prev => {
                    return prev.map(msg => {
                      if (msg.type === 'digitalHuman' && msg.timestamp === tempMessageId) {
                        return {
                          ...msg,
                          content: tempResponse,
                          isStreaming: true // 保持流式标记
                        };
                      }
                      return msg;
                    });
                  });

                  // 如果是最后一个数据包，记录最终响应
                  if (!data.partial) {
                    finalResponse = content;
                  }
                }
              }
            } catch (e) {
              console.error('解析流数据失败:', e);
            }
          }
        }
      }

      // 使用最终响应或临时响应作为结果
      const aiResponse = finalResponse || tempResponse;

      // 检查是否包含关键词并切换背景
      const currentConfig = configRef.current;
      if (currentConfig.keywordBackgrounds && currentConfig.keywordBackgrounds.length > 0) {
        console.log('当前配置的关键词展示:', currentConfig.keywordBackgrounds);
        console.log('AI回复内容:', aiResponse);

        const matchedBackground = currentConfig.keywordBackgrounds.find(bg =>
          bg.keyword &&
          aiResponse.includes(bg.keyword) &&
          (bg.triggerType === 'aiReply' || bg.triggerType === 'both' || !bg.triggerType)
        );

        console.log('匹配到的背景:', matchedBackground);

        if (matchedBackground) {
          console.log('准备切换背景到:', matchedBackground.url);

          // 设置关键词触发状态
          setKeywordTriggered(true);

          // 如果启用了轮播暂停机制，暂停轮播
          if (currentConfig.carouselPauseOnKeyword && carouselActive) {
            pauseCarousel();
          }

          // 🔥 修复：AI回复触发的背景切换应该保持视频声音状态
          // 检查当前是否有视频声音启用，如果有则保持
          const shouldKeepSound = videoSoundEnabledRef.current;
          console.log('AI回复触发背景切换，是否保持视频声音:', shouldKeepSound);

          changeBackground(matchedBackground.url, getResourceType(matchedBackground.url), shouldKeepSound);
          console.log('背景状态已更新');
        }
      }

      // 最后一次更新消息，确保内容完整
      setMessages(prev => {
        return prev.map(msg => {
          if (msg.type === 'digitalHuman' && msg.timestamp === tempMessageId) {
            return {
              ...msg,
              content: aiResponse,
              isStreaming: false // 流式响应结束，移除标记
            };
          }
          return msg;
        });
      });

      return aiResponse;
    } catch (error) {
      console.error('AI API调用失败:', error);

      // 更新错误消息
      setMessages(prev => {
        // 尝试查找临时消息并更新为错误信息
        const hasTemporaryMessage = prev.some(msg => msg.type === 'digitalHuman' && msg.content === '');

        if (hasTemporaryMessage) {
          return prev.map(msg => {
            if (msg.type === 'digitalHuman' && msg.content === '') {
              return {
                ...msg,
                content: '抱歉，我现在无法回答这个问题。',
                isStreaming: false // 确保移除流式标记
              };
            }
            return msg;
          });
        } else {
          // 如果没有找到临时消息，添加一个新的错误消息
          return [...prev, {
            type: 'digitalHuman' as const,
            content: '抱歉，我现在无法回答这个问题。',
            timestamp: Date.now(),
            isStreaming: false
          }];
        }
      });

      return '抱歉，我现在无法回答这个问题。';
    }
  };

  // 修改 updateWakeupState 函数中的显示内容设置
  const updateWakeupState = useCallback((active: boolean) => {
    const now = Date.now();
    console.log('更新唤醒状态 - 开始:', {
      当前值: {
        wakeupStateRef: wakeupStateRef.current.isActive,
        isWakeupActive
      },
      将设置为: active
    });
    
    // 检查是否是从未唤醒状态变为唤醒状态
    const isFirstTimeWakeup = !wakeupStateRef.current.isActive && active;
    
    wakeupStateRef.current.isActive = active;
    wakeupStateRef.current.lastUpdateTime = now;
    setIsWakeupActive(active);
    
    console.log('更新唤醒状态 - 完成:', {
      新值: {
        wakeupStateRef: wakeupStateRef.current.isActive,
        isWakeupActive: '(React状态更新是异步的，此处可能未反映最新值)'
      },
      是首次唤醒: isFirstTimeWakeup
    });

    // 清除之前的超时检查定时器
    if (wakeupStateRef.current.timeoutId) {
      clearTimeout(wakeupStateRef.current.timeoutId);
      wakeupStateRef.current.timeoutId = null;
    }

    // 如果激活唤醒状态，设置一个定期检查的定时器
    if (active) {
      const currentConfig = configRef.current;
      
      // 设置显示内容为"已唤醒"
      setDisplayContent("已唤醒");
      
      // 只在首次唤醒时且还未显示过欢迎消息时添加欢迎消息并驱动数字人说话
      if (isFirstTimeWakeup && !hasShownWelcomeRef.current) {
        // 添加欢迎消息到聊天框
        const welcomeMessage = "您好，请问有什么可以帮助您的";
        setMessages(prev => [...prev, {
          type: 'digitalHuman',
          content: welcomeMessage,
          timestamp: Date.now()
        }]);
        
        // 驱动数字人说出欢迎消息
        dhIframe.sendMessage({
          action: 'TEXT_RENDER',
          body: welcomeMessage,
          requestId: commandId
        });
        setIsDigitalHumanSpeaking(true);
        
        // 标记已显示欢迎消息
        hasShownWelcomeRef.current = true;
      }
      
      // 创建一个定期检查函数
      const checkInactivity = () => {
        const currentTime = Date.now();
        const timeSinceLastUpdate = currentTime - wakeupStateRef.current.lastUpdateTime;
        
        console.log('检查唤醒状态活跃度:', {
          当前时间: new Date(currentTime).toLocaleTimeString(),
          上次活动时间: new Date(wakeupStateRef.current.lastUpdateTime).toLocaleTimeString(),
          不活跃时长: Math.floor(timeSinceLastUpdate / 1000) + '秒',
          超时阈值: currentConfig.voiceWakeupTimeout + '秒',
          唤醒状态: wakeupStateRef.current.isActive ? '已唤醒' : '未唤醒'
        });
        
        // 如果超过配置的超时时间没有语音活动，重置唤醒状态
        if (timeSinceLastUpdate > currentConfig.voiceWakeupTimeout * 1000) {
          console.log('唤醒状态超时，自动重置为未唤醒');
          wakeupStateRef.current.isActive = false;
          setIsWakeupActive(false);
          setDisplayContent(getWakeupPrompt());
          
          // 重置欢迎消息状态，下次唤醒时可以再次显示欢迎消息
          hasShownWelcomeRef.current = false;
          
          return; // 不再继续检查
        }
        
        // 如果未超时，继续定期检查
        wakeupStateRef.current.timeoutId = setTimeout(checkInactivity, 5000); // 每5秒检查一次
      };
      
      // 启动定期检查
      wakeupStateRef.current.timeoutId = setTimeout(checkInactivity, 5000); // 首次5秒后检查
    }
  }, [getWakeupPrompt, commandId]);

  // 轮播管理逻辑
  const startCarousel = useCallback(() => {
    const currentConfig = configRef.current;

    if (!currentConfig.carouselEnabled || !currentConfig.carouselItems?.length) {
      console.log('轮播功能未启用或无轮播项目');
      return;
    }

    // 过滤出启用的轮播项目
    const enabledItems = currentConfig.carouselItems.filter(item => item.enabled);
    if (enabledItems.length === 0) {
      console.log('没有启用的轮播项目');
      return;
    }

    carouselItemsRef.current = enabledItems;
    setCarouselActive(true);
    setCarouselCurrentIndex(0);
    setCarouselPaused(false);

    console.log('轮播已启动，共', enabledItems.length, '个项目');

    // 立即显示第一个轮播项目
    if (enabledItems.length > 0) {
      showCarouselItem(0);
    }
  }, []);

  const stopCarousel = useCallback(() => {
    if (carouselTimerRef.current) {
      clearTimeout(carouselTimerRef.current);
      carouselTimerRef.current = null;
    }
    setCarouselActive(false);
    setCarouselPaused(false);
    console.log('轮播已停止');
  }, []);

  const pauseCarousel = useCallback(() => {
    if (carouselTimerRef.current) {
      clearTimeout(carouselTimerRef.current);
      carouselTimerRef.current = null;
    }
    setCarouselPaused(true);
    console.log('轮播已暂停');
  }, []);

  const showCarouselItem = useCallback(async (index: number) => {
    const items = carouselItemsRef.current;
    if (!items || items.length === 0 || index >= items.length) return;

    const item = items[index];
    if (!item || !item.backgroundId) return;

    try {
      // 从上传服务获取背景列表
      const uploadService = UploadService.getInstance();
      const backgrounds = await uploadService.getUploadedBackgrounds();

      // 根据backgroundId查找对应的背景
      const background = backgrounds.find(bg => bg.id === item.backgroundId);

      if (background) {
        console.log('显示轮播项目:', index, background.name);
        const resourceType = getResourceType(background.url);
        setCurrentCarouselItemType(resourceType);
        changeBackground(background.url, resourceType);
      } else {
        console.warn('未找到对应的背景资源:', item.backgroundId);
      }
    } catch (error) {
      console.error('获取背景信息失败:', error);
    }
  }, []);

  const scheduleNextCarouselItem = useCallback(() => {
    const currentConfig = configRef.current;
    if (!carouselActive || carouselPaused) return;

    // 如果启用了等待视频结束且当前是视频，则不设置定时器，等待onended事件
    if (currentConfig.carouselWaitForVideoEnd && currentCarouselItemType === 'video') {
      console.log('等待视频播放完成，不设置定时器');
      return;
    }

    carouselTimerRef.current = setTimeout(() => {
      const items = carouselItemsRef.current;
      if (items.length === 0) return;

      const nextIndex = (carouselCurrentIndex + 1) % items.length;
      setCarouselCurrentIndex(nextIndex);
      showCarouselItem(nextIndex);

      // 继续调度下一个项目
      scheduleNextCarouselItem();
    }, currentConfig.carouselInterval * 1000);
  }, [carouselActive, carouselPaused, carouselCurrentIndex, currentCarouselItemType, showCarouselItem]);

  const resumeCarousel = useCallback(() => {
    if (carouselActive && carouselPaused) {
      setCarouselPaused(false);
      // 立即切换到下一个轮播项，而不是只调度下一个
      const items = carouselItemsRef.current;
      if (items.length > 0) {
        const nextIndex = (carouselCurrentIndex + 1) % items.length;
        setCarouselCurrentIndex(nextIndex);
        showCarouselItem(nextIndex);
        console.log('轮播已恢复，立即显示下一项:', nextIndex);
        // 继续调度后续项目
        scheduleNextCarouselItem();
      } else {
        console.log('轮播已恢复，但没有可用项目');
        scheduleNextCarouselItem();
      }
    }
  }, [carouselActive, carouselPaused, carouselCurrentIndex, showCarouselItem, scheduleNextCarouselItem]);

  // 手动触发下一个轮播项目（用于视频播放完成时调用）
  const triggerNextCarouselItem = useCallback(() => {
    if (!carouselActive || carouselPaused) return;

    const items = carouselItemsRef.current;
    if (items.length === 0) return;

    const nextIndex = (carouselCurrentIndex + 1) % items.length;
    setCarouselCurrentIndex(nextIndex);
    showCarouselItem(nextIndex);

    // 调度下一个项目
    scheduleNextCarouselItem();
  }, [carouselActive, carouselPaused, carouselCurrentIndex, showCarouselItem, scheduleNextCarouselItem]);

  // 重置语音识别状态
  const resetStates = () => {
    recognitionStateRef.current = {
      isStarting: false,
      isStopping: false,
      isActive: false,
      retryCount: 0,
      lastError: null,
      lastStartTime: 0,
      cooldownEndTime: 0,
      isProcessing: false
    };
    setIsProcessing(false);
    setIsDigitalHumanSpeaking(false);
  };

  // 检查是否可以启动语音识别
  const canStartRecognition = () => {
    const now = Date.now();
    const state = recognitionStateRef.current;

    if (state.isStarting || state.isStopping) {
      console.log('语音识别正在启动或停止中，忽略请求');
      return false;
    }

    if (state.isActive) {
      console.log('语音识别已经在运行中，忽略请求');
      return false;
    }

    if (now < state.cooldownEndTime) {
      console.log('冷却时间未到，忽略启动请求');
      return false;
    }

    if (now - state.lastStartTime < minStartInterval) {
      console.log('距离上次启动时间太短，忽略请求');
      return false;
    }

    return true;
  };

  // 修改启动语音识别函数
  const startRecognition = (recognition: any) => {
    if (!canStartRecognition()) {
      return;
    }

    try {
      // 确保先停止之前的识别
      if (recognitionStateRef.current.isActive) {
        stopRecognition(recognition);
        return;
      }

      console.log('开始启动语音识别...');
      recognitionStateRef.current.isStarting = true;
      recognitionStateRef.current.lastStartTime = Date.now();
      
      // 检查是否是阿里云语音识别实例
      if (recognition instanceof AliASRRecognition) {
        console.log('启动阿里云语音识别');
        recognition.start();
      } else {
        // 浏览器原生语音识别
        console.log('启动浏览器原生语音识别');
        recognition.start();
      }
    } catch (error) {
      console.error('启动语音识别失败:', error);
      recognitionStateRef.current.isStarting = false;
      recognitionStateRef.current.lastError = error instanceof Error ? error.message : '未知错误';
      recognitionStateRef.current.cooldownEndTime = Date.now() + cooldownPeriod;
    }
  };

  // 修改停止语音识别函数
  const stopRecognition = (recognition: any) => {
    if (!recognition || !recognitionStateRef.current.isActive) {
      return;
    }

    try {
      console.log('开始停止语音识别...');
      recognitionStateRef.current.isStopping = true;
      
      // 检查是否是阿里云语音识别实例
      if (recognition instanceof AliASRRecognition) {
        console.log('停止阿里云语音识别');
        recognition.stop();
      } else {
        // 浏览器原生语音识别
        console.log('停止浏览器原生语音识别');
        recognition.stop();
      }
    } catch (error) {
      console.error('停止语音识别失败:', error);
    } finally {
      recognitionStateRef.current.isStopping = false;
      recognitionStateRef.current.isActive = false;
    }
  };

  // 添加字符串相似度检查函数
  const calculateSimilarity = (str1: string, str2: string): number => {
    // 预处理：去除标点符号和空格
    const cleanStr1 = str1.replace(/[^\w\s\u4e00-\u9fa5]/g, '').replace(/\s+/g, '');
    const cleanStr2 = str2.replace(/[^\w\s\u4e00-\u9fa5]/g, '').replace(/\s+/g, '');
    
    // 如果预处理后字符串完全相同，直接返回1
    if (cleanStr1 === cleanStr2) {
      return 1;
    }
    
    const len1 = cleanStr1.length;
    const len2 = cleanStr2.length;
    const matrix: number[][] = [];

    // 如果任一字符串为空，返回0
    if (len1 === 0 || len2 === 0) {
      return 0;
    }

    // 初始化编辑距离矩阵
    for (let i = 0; i <= len1; i++) {
      matrix[i] = [i];
    }
    for (let j = 0; j <= len2; j++) {
      matrix[0][j] = j;
    }

    // 计算编辑距离
    for (let i = 1; i <= len1; i++) {
      for (let j = 1; j <= len2; j++) {
        if (cleanStr1[i - 1] === cleanStr2[j - 1]) {
          matrix[i][j] = matrix[i - 1][j - 1];
        } else {
          matrix[i][j] = Math.min(
            matrix[i - 1][j - 1] + 1, // 替换
            matrix[i][j - 1] + 1,     // 插入
            matrix[i - 1][j] + 1      // 删除
          );
        }
      }
    }

    // 计算相似度
    const maxLen = Math.max(len1, len2);
    const similarity = 1 - matrix[len1][len2] / maxLen;
    
    // 调试输出
    console.log('相似度详情:', {
      original1: str1,
      original2: str2,
      cleaned1: cleanStr1,
      cleaned2: cleanStr2,
      editDistance: matrix[len1][len2],
      maxLength: maxLen,
      similarity: similarity
    });
    
    return similarity;
  };

  // 添加打断关键词检测函数
  const isInterruptionPhrase = (text: string): boolean => {
    // 获取当前配置
    const currentConfig = configRef.current;

    // 使用配置中的interruptionKeywords，如果没有则使用默认值
    const interruptionKeywords = currentConfig.interruptionKeywords?.length ?
      currentConfig.interruptionKeywords : defaultInterruptionKeywords;

    // 如果没有设置打断关键词，返回 false
    if (!interruptionKeywords.length) {
      return false;
    }

    // 获取相似度阈值，如果未配置则使用默认值0.5
    const threshold = currentConfig.similarityThreshold !== undefined ? 
      currentConfig.similarityThreshold : 0.5;

    console.log('检查打断关键词:', { text, interruptionKeywords, threshold });

    // 检查是否匹配任何一个打断关键词
    for (const keyword of interruptionKeywords) {
      const similarity = calculateSimilarity(text, keyword);
      console.log('打断关键词相似度检查:', {
        text,
        keyword,
        similarity,
        threshold
      });
      if (similarity >= threshold) {
        console.log('匹配到打断关键词 (相似度匹配):', keyword);
        return true;
      }
    }

    return false;
  };

  // 添加视频播放命令检测函数
  const isVideoPlayCommand = (text: string): boolean => {
    // 获取当前配置
    const currentConfig = configRef.current;

    // 如果未启用视频声音控制功能，直接返回false
    if (!currentConfig.videoSoundEnabled) {
      return false;
    }

    // 使用配置中的videoPlayPhrases
    const videoPlayPhrases = currentConfig.videoPlayPhrases || [];

    // 如果没有设置播放视频的触发短语，返回 false
    if (!videoPlayPhrases.length) {
      return false;
    }

    console.log('检查视频播放命令:', { text, videoPlayPhrases });

    // 检查是否匹配任何一个播放视频的触发短语
    for (const phrase of videoPlayPhrases) {
      if (phrase && text.includes(phrase)) {
        console.log('匹配到视频播放命令:', phrase);
        return true;
      }
    }

    return false;
  };

  // 修改唤醒词检查函数
  const isWakeupPhrase = (text: string): boolean => {
    // 优先使用 configRef 中的配置，避免异步更新导致的不一致问题
    const currentConfig = configRef.current;

    // 获取当前配置的唤醒词列表
    const wakeupPhrases = currentConfig.voiceWakeupPhrases || [];

    console.log('isWakeupPhrase函数内的config:', JSON.stringify(config.voiceWakeupPhrases));
    console.log('isWakeupPhrase函数内的configRef:', JSON.stringify(configRef.current.voiceWakeupPhrases));

    // 如果没有配置唤醒词，返回 false
    if (!wakeupPhrases.length) {
      console.log('未配置唤醒词');
      return false;
    }

    // 获取相似度阈值，如果未配置则使用默认值0.5
    const threshold = currentConfig.similarityThreshold !== undefined ? 
      currentConfig.similarityThreshold : 0.5;

    console.log('当前配置的唤醒词列表:', wakeupPhrases);
    console.log('使用的相似度阈值:', threshold);

    // 检查是否匹配任何一个唤醒词
    let matchResult = false;
    for (const phrase of wakeupPhrases) {
      const similarity = calculateSimilarity(text, phrase);
      console.log('唤醒词相似度检查:', {
        text,
        phrase,
        similarity,
        threshold
      });

      if (similarity >= threshold) {
        matchResult = true;
        console.log('匹配到唤醒词:', phrase);
        break;
      }
    }

    console.log('唤醒词匹配结果:', matchResult);
    return matchResult;
  };

  // 修改语音识别结果处理
  useEffect(() => {
    // 检查是否启用阿里云语音识别
    const useAliASR = configRef.current.aliASREnabled && configRef.current.aliASRApiKey;
    
    if (useAliASR && AliASRRecognition.isSupported()) {
      console.log('使用阿里云语音识别服务');
      
      try {
        // 创建阿里云语音识别实例
        const recognition = new AliASRRecognition({
          continuous: true,
          interimResults: true,
          lang: 'zh-CN',
          debug: true, // 启用调试模式，输出更详细的日志
          resultTimeoutMs: configRef.current.aliASRResultTimeoutMs || 4000, // 增加结果超时时间配置，默认4000毫秒
          silenceTimeoutMs: configRef.current.aliASRResultTimeoutMs || 4000 // 使用相同的超时时间配置静音检测，默认4000毫秒
        });
        
        // 设置事件处理器
        recognition.onstart = () => {
          console.log('语音识别开始');
          recognitionStateRef.current.isStarting = false;
          recognitionStateRef.current.isActive = true;
          recognitionStateRef.current.retryCount = 0;
          recognitionStateRef.current.lastError = null;
          setIsRecording(true);
          silenceStartTimeRef.current = Date.now();
          
          // 更新连接状态
          setRecognitionState(prev => ({
            ...prev,
            isConnecting: false,
            isConnected: true,
            hasError: false,
            errorMessage: '',
            reconnecting: false
          }));
        };

        // 添加onresult事件处理器
        recognition.onresult = (event) => {
          console.log('收到语音识别结果事件:', event);
          
          // 检查是否有识别结果
          if (!event.results || Object.keys(event.results).length === 0) {
            return;
          }
          
          // 获取最新的识别结果
          const lastResultIndex = Object.keys(event.results).length - 1;
          const lastResult = event.results[lastResultIndex];
          
          if (!lastResult || !lastResult[0]) {
            return;
          }
          
          const transcript = lastResult[0].transcript;
          const isFinal = lastResult.isFinal;
          
          console.log(`语音识别结果: "${transcript}", 最终: ${isFinal}`);
          
          // 检测到语音活动，重置唤醒状态的最后更新时间
          if (wakeupStateRef.current.isActive) {
            wakeupStateRef.current.lastUpdateTime = Date.now();
            console.log('检测到语音活动，重置唤醒超时计时');
          }
          
          // 处理语音识别结果 - 实现软终止逻辑
          if (isFinal && softTerminationEnabledRef.current) {
            // 清除之前的定时器
            if (finalTimerRef.current) {
              clearTimeout(finalTimerRef.current);
              finalTimerRef.current = null;
            }
            
            // 保存当前文本
            setPendingFinalText(transcript);
            console.log(`软终止: 收到最终结果，但暂存等待 ${softTerminationDelayRef.current}ms:`, transcript);
            
            // 设置新的定时器，延迟处理最终结果
            finalTimerRef.current = setTimeout(() => {
              console.log('软终止: 延迟结束，处理最终结果:', pendingFinalText || transcript);
              // 清空中间结果
              setInterimText('');
              // 处理最终结果
              handleSpeechResult(pendingFinalText || transcript);
              // 清空暂存文本
              setPendingFinalText('');
              finalTimerRef.current = null;
            }, softTerminationDelayRef.current);
            
            // 同时更新中间结果显示，让用户看到当前识别内容
            setInterimText(transcript);
          } else if (isFinal && !softTerminationEnabledRef.current) {
            // 如果未启用软终止，则直接处理最终结果
            setInterimText('');
            handleSpeechResult(transcript);
          } else {
            // 更新中间结果显示
            setInterimText(transcript);
          }
        };

        recognition.onerror = (event: any) => {
          console.error('语音识别错误:', event.error);
          recognitionStateRef.current.isActive = false;
          recognitionStateRef.current.lastError = event.error;

          // 更新错误状态
          setRecognitionState(prev => ({
            ...prev,
            hasError: true,
            errorMessage: event.error,
            isConnected: false
          }));

          // 显示错误消息
          if (event.error === 'server') {
            setDisplayContent('语音识别服务错误，请检查配置');
          } else if (event.error === 'network') {
            setDisplayContent('网络连接错误，请检查网络');
          } else if (event.error === 'not-allowed') {
            setDisplayContent('无法访问麦克风，请检查权限');
          } else if (event.error === 'aborted') {
            if (recognitionStateRef.current.isStopping) {
              console.log('正常中止');
              return;
            }
            console.log('异常中止');
          }

          // 如果启用了语音唤醒，则尝试重新启动
          if (config.voiceWakeupEnabled) {
            if (recognitionStateRef.current.retryCount < maxRetries) {
              recognitionStateRef.current.retryCount++;
              console.log(`尝试重新启动语音识别 (${recognitionStateRef.current.retryCount}/${maxRetries})`);

              // 更新重连状态
              setRecognitionState(prev => ({
                ...prev,
                reconnecting: true,
                reconnectCount: recognitionStateRef.current.retryCount
              }));

              // 确保完全停止后再重试
              stopRecognition(recognition);

              // 设置冷却时间
              recognitionStateRef.current.cooldownEndTime = Date.now() + cooldownPeriod;

              setTimeout(() => {
                if (canStartRecognition()) {
                  // 更新连接状态
                  setRecognitionState(prev => ({
                    ...prev,
                    isConnecting: true
                  }));
                  
                  startRecognition(recognition);
                } else {
                  // 如果无法启动，重置状态后再尝试
                  resetStates();
                  setTimeout(() => {
                    // 更新连接状态
                    setRecognitionState(prev => ({
                      ...prev,
                      isConnecting: true
                    }));
                    
                    startRecognition(recognition);
                  }, 1000);
                }
              }, retryDelay);
            } else {
              console.error('语音识别重试次数超过限制，请刷新页面重试');
              setDisplayContent('语音识别出现错误，请刷新页面重试');
              resetStates();

              // 更新重连状态
              setRecognitionState(prev => ({
                ...prev,
                reconnecting: false,
                hasError: true,
                errorMessage: '重连失败，请刷新页面'
              }));

              // 等待一段时间后尝试完全重置并重启
              setTimeout(() => {
                resetStates();
                
                // 更新连接状态
                setRecognitionState(prev => ({
                  ...prev,
                  isConnecting: true,
                  reconnecting: true,
                  reconnectCount: 0
                }));
                
                startRecognition(recognition);
              }, 5000);
            }
          }
        };

        // 添加连接状态事件处理
        const originalStart = recognition.start;
        recognition.start = async function() {
          // 更新连接状态
          setRecognitionState(prev => ({
            ...prev,
            isConnecting: true,
            hasError: false,
            errorMessage: ''
          }));
          
          try {
            // 注意：如果需要使用VAD参数，需要修改AliASRRecognition类以支持参数传递
            // 目前使用增加超时时间和软终止方案来解决问题
            await originalStart.apply(this);
          } catch (error) {
            // 更新错误状态
            setRecognitionState(prev => ({
              ...prev,
              isConnecting: false,
              hasError: true,
              errorMessage: error instanceof Error ? error.message : '连接失败'
            }));
            throw error;
          }
        };
        
        setRecognition(recognition);
      } catch (error) {
        console.error('初始化阿里云语音识别失败:', error);
        setDisplayContent('初始化语音识别失败，请检查配置');
        
        // 更新错误状态
        setRecognitionState(prev => ({
          ...prev,
          hasError: true,
          errorMessage: error instanceof Error ? error.message : '初始化失败'
        }));
        
        // 回退到浏览器原生语音识别
        if ('webkitSpeechRecognition' in window) {
          console.log('回退到浏览器原生语音识别');
          initWebkitSpeechRecognition();
        }
      }
    } else if ('webkitSpeechRecognition' in window) {
      console.log('使用浏览器原生语音识别服务');
      initWebkitSpeechRecognition();
    }
    
    // 浏览器原生语音识别初始化函数
    function initWebkitSpeechRecognition() {
      const recognition = new (window as any).webkitSpeechRecognition();
      recognition.continuous = true;
      recognition.interimResults = true;
      recognition.lang = 'zh-CN';

      recognition.onstart = () => {
        console.log('语音识别开始');
        recognitionStateRef.current.isStarting = false;
        recognitionStateRef.current.isActive = true;
        recognitionStateRef.current.retryCount = 0;
        recognitionStateRef.current.lastError = null;
        setIsRecording(true);
        silenceStartTimeRef.current = Date.now();
        
        // 更新连接状态
        setRecognitionState(prev => ({
          ...prev,
          isConnecting: false,
          isConnected: true,
          hasError: false,
          errorMessage: ''
        }));
      };

      // 添加onresult事件处理器
      recognition.onresult = (event: any) => {
        console.log('收到浏览器语音识别结果事件');
        
        // 获取最新的识别结果
        const lastResultIndex = event.results.length - 1;
        const transcript = event.results[lastResultIndex][0].transcript;
        const isFinal = event.results[lastResultIndex].isFinal;
        
        console.log(`浏览器语音识别结果: "${transcript}", 最终: ${isFinal}`);
        
        // 检测到语音活动，重置唤醒状态的最后更新时间
        if (wakeupStateRef.current.isActive) {
          wakeupStateRef.current.lastUpdateTime = Date.now();
          console.log('检测到语音活动，重置唤醒超时计时');
        }
        
        // 处理语音识别结果
        if (isFinal) {
          // 清空中间结果
          setInterimText('');
          // 处理最终结果
          handleSpeechResult(transcript);
        } else {
          // 更新中间结果显示
          setInterimText(transcript);
        }
      };

      // 添加错误处理
      recognition.onerror = (event: any) => {
        console.error('浏览器语音识别错误:', event.error);
        recognitionStateRef.current.isActive = false;
        recognitionStateRef.current.lastError = event.error;
        
        // 更新错误状态
        setRecognitionState(prev => ({
          ...prev,
          hasError: true,
          errorMessage: event.error,
          isConnected: false
        }));
      };

      // 添加结束处理
      recognition.onend = () => {
        console.log('浏览器语音识别结束');
        recognitionStateRef.current.isActive = false;
        
        // 如果启用了语音唤醒，尝试重新启动
        if (config.voiceWakeupEnabled && hasManuallyStarted) {
          console.log('语音唤醒已启用，尝试重新启动语音识别');
          setTimeout(() => {
            if (canStartRecognition()) {
              startRecognition(recognition);
            }
          }, 1000);
        }
      };
    
      setRecognition(recognition);
    }
  }, [commandId, videoIsMuted, config.voiceWakeupEnabled, config.voiceWakeupPhrases, updateWakeupState]);

  // 修改开始录音函数 - 添加手动启动逻辑
  const startRecording = () => {
    if (recognition) {
      // 标记用户已手动启动
      setHasManuallyStarted(true);

      if (config.voiceWakeupEnabled) {
        console.log('开始录音 - 唤醒模式已启用，当前唤醒状态:', {
          isWakeupActive,
          wakeupRefIsActive: wakeupStateRef.current.isActive
        });
        
        startRecognition(recognition);
        if (!wakeupStateRef.current.isActive) {
          setDisplayContent(getWakeupPrompt());
        }
        return;
      }

      // 如果未启用语音唤醒，则按原有逻辑处理
      if (!isDigitalHumanSpeaking || config.allowInterruption) {
        // 如果数字人正在说话且允许打断，发送打断命令
        if (isDigitalHumanSpeaking && config.allowInterruption) {
          // 使用智能打断，不发送后续响应
          intelligentInterrupt();
        }
        startRecognition(recognition);
      }
    }
  };

  // 修改停止录音函数
  const stopRecording = () => {
    if (recognition) {
      // 如果启用了语音唤醒，则不允许停止录音
      if (config.voiceWakeupEnabled) {
        return;
      }
      stopRecognition(recognition);
    }
  };

  // 检查是否可以自动播放
  const checkPlayUnMute = async (): Promise<boolean> => {
    if (typeof navigator.userActivation !== 'undefined' && typeof navigator.userActivation.hasBeenActive !== 'undefined') {
      return navigator.userActivation.hasBeenActive;
    }
    return new Promise((resolve) => {
      const audioElem: HTMLAudioElement = document.createElement('audio');
      audioElem.src = 'data:audio/wav;base64,UklGRigAAABXQVZFZm10IBAAAAABAAEAESsAACJWAAACABAAZGF0YQAAAAA=';
      audioElem.muted = false;

      const playPromise: Promise<void> | undefined = audioElem.play();

      if (playPromise !== undefined) {
        playPromise
          .then(() => {
            resolve(true);
          })
          .catch(() => {
            resolve(false);
          });
        audioElem.remove();
      } else {
        audioElem.remove();
        resolve(false);
      }
    });
  };

  // 用户交互处理函数
  const handleUserInteraction = useCallback(async () => {
    if (audioState.isMuted && !audioState.hasUserInteracted) {
      const canPlay = await checkPlayUnMute();
      if (canPlay) {
        setAudioState(prev => ({
          ...prev,
          isMuted: false,
          hasUserInteracted: true
        }));
        dhIframe.sendCommand({
          subType: 'muteAudio',
          subContent: false
        });
      }
    }
  }, [audioState.isMuted, audioState.hasUserInteracted]);

  // 自动播放恢复机制
  const attemptUnmute = useCallback(async () => {
    if (audioState.isMuted && !audioState.hasUserInteracted) {
      const canPlay = await checkPlayUnMute();
      if (canPlay) {
        setAudioState(prev => ({
          ...prev,
          isMuted: false,
          hasUserInteracted: true
        }));
        dhIframe.sendCommand({
          subType: 'muteAudio',
          subContent: false
        });
      }
    }
  }, [audioState.isMuted, audioState.hasUserInteracted]);

  // 添加用户交互事件监听
  useEffect(() => {
    const handleInteraction = () => {
      handleUserInteraction();
    };

    document.addEventListener('click', handleInteraction);
    document.addEventListener('touchstart', handleInteraction);
    document.addEventListener('keydown', handleInteraction);

    return () => {
      document.removeEventListener('click', handleInteraction);
      document.removeEventListener('touchstart', handleInteraction);
      document.removeEventListener('keydown', handleInteraction);
    };
  }, [handleUserInteraction]);

  // 修改 onMessage 回调
  const onMessage = useCallback((msg: any) => {
    if (msg.origin === 'https://open.xiling.baidu.com') {
      const { type, content } = msg.data;
      const { action, requestId } = content;
      const currentConfig = configRef.current;

      switch (type) {
        case 'rtcState':
          if (content.action === 'remoteVideoConnected') {
            setRealTimeVideoReady(true);
            // 尝试恢复音频
            attemptUnmute();
          }
          if (content.action === 'localVideoMuted' && content.body) {
            setAudioState(prev => ({
              ...prev,
              isMuted: true
            }));
          }
          break;
        case 'wsState':
          if (content.readyState === 1) {
            setWsConnected(true);
          } else if (content.readyState === 3 || content.readyState === 2) {
            setWsConnected(false);
          }
          break;
        case 'msg':
          console.log(`收到数字人消息事件:`, { action, requestId, commandId, type: content.type });
          if (action === 'RENDER_START') {
            console.log(`数字人开始渲染, 驱动id为${requestId}, 当前commandId为${commandId}`);
            setIsRenderingStarted(true);
            setCurrentRenderRequestId(requestId);
          }
          else if (requestId === commandId && action === 'FINISHED') {
            console.info(`数字人驱动完成, 驱动id为${requestId}, 当前commandId为${commandId}`);
            setIsDigitalHumanSpeaking(false);
            setIsRenderingStarted(false);
            setCurrentRenderRequestId('');

            // 检查是否需要自动重置背景
            if (keywordTriggered && currentConfig.autoResetBackground) {
              console.log('关键词展示完成，准备自动重置到默认背景');

              // 获取当前背景类型
              const isVideoBackground = currentBackgroundType === 'video';

              // 如果是视频类型，不在此处设置自动切换，而是等待视频播放完成时处理
              if (isVideoBackground) {
                console.log('当前是视频背景，将在播放完成后自动切换');
                return;
              }

              // 为图片类型重置状态，视频类型将在播放完成时重置
              setKeywordTriggered(false);

              // 仅对图片类型设置延迟切换，给用户一点时间查看
              keywordResetTimeoutRef.current = setTimeout(() => {
                // 恢复轮播状态
                if (currentConfig.carouselEnabled) {
                  console.log('图片展示完成，恢复轮播展示');
                  if (currentConfig.carouselPauseOnKeyword) {
                    resumeCarousel();
                  }
                  return;
                }

                // 如果轮播未启用，则切换回默认背景
                if (currentConfig.defaultBackgroundUrl) {
                  console.log('切换回默认背景:', currentConfig.defaultBackgroundUrl);
                  const resourceType = getResourceType(currentConfig.defaultBackgroundUrl);
                  changeBackground(currentConfig.defaultBackgroundUrl, resourceType);
                }
              }, currentConfig.keywordDisplayDuration * 1000); // 使用配置的展示时间
            }

            // 数字人说完话后，只有在用户已经手动启动过的情况下才自动重新开启麦克风
            if (recognition && !recognitionStateRef.current.isProcessing && hasManuallyStarted) {
              if (currentConfig.voiceWakeupEnabled || currentConfig.allowInterruption) {
                console.log('数字人说话结束，自动重新开启麦克风');
                console.log('当前唤醒状态:', {
                  isWakeupActive,
                  wakeupRefIsActive: wakeupStateRef.current.isActive,
                  recognitionActive: recognitionStateRef.current.isActive
                });

                // 如果语音识别未处于活动状态，则重新启动它
                if (!recognitionStateRef.current.isActive) {
                  startRecognition(recognition);
                  setIsRecording(true);
                  
                  // 如果唤醒模式已启用但未激活，确保显示正确的提示
                  if (currentConfig.voiceWakeupEnabled && !wakeupStateRef.current.isActive) {
                    console.log('唤醒模式已启用但未激活，显示唤醒提示');
                    setDisplayContent(getWakeupPrompt());
                  }
                } else {
                  console.log('语音识别已经在运行，不需要重启');
                }
              }
            }
          }
          else if (action === 'RENDER_INTERRUPTED') {
            console.log(`数字人播报被打断, 驱动id为${requestId}`);
            setIsDigitalHumanSpeaking(false);
            setIsRenderingStarted(false);
            setCurrentRenderRequestId('');
          }
          else if (action === 'TEXT_RENDER') {
            console.log(`数字人开始说话, 驱动id为${requestId}, 当前commandId为${commandId}`);
            setIsDigitalHumanSpeaking(true);

            // 如果正在录音且不允许打断且未启用语音唤醒且打断功能未启用，则停止录音
            // 否则保持语音识别活动状态，以便能检测打断关键词
            if (isRecording && !currentConfig.allowInterruption && !currentConfig.voiceWakeupEnabled) {
              console.log('数字人开始说话，停止录音（非唤醒模式）');
              stopRecognition(recognition);
              setIsRecording(false);
            } else {
              console.log('数字人开始说话，但保持麦克风开启以监听可能的打断');
            }
          }
          break;
        default:
          break;
      }
    }
  }, [
    attemptUnmute,
    recognition,
    isRecording,
    commandId,
    keywordTriggered,
    startRecognition,
    stopRecognition,
    setIsRecording,
    currentBackgroundType,
    resumeCarousel,
    getResourceType,
    changeBackground,
    hasManuallyStarted,
    getWakeupPrompt
  ]);

  // 处理输入变化
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setInputText(e.target.value);
  };

  // 处理发送消息
  const handleSendMessage = async (text?: string, fromSpeech: boolean = false) => {
    // 优先使用传入的文本，如果没有则使用输入框中的文本
    const messageText = text || inputText;

    if (messageText.trim()) {
      setIsProcessing(true);
      const currentConfig = configRef.current;
      const userInputText = messageText.trim();

      // ===== 最优先判断：数字人驱动状态和视频播放状态 =====
      // 无论语音输入还是文字输入，都先判断当前数字人是否驱动完成，展示视频是否播放完成
      // 如果未完成，只能用打断关键词进行打断

      // 🔥 修复：使用 ref 获取最新的状态，确保语音输入时能正确获取状态
      const currentVideoSoundEnabled = videoSoundEnabledRef.current;
      const currentIsDigitalHumanSpeaking = isDigitalHumanSpeakingRef.current;
      console.log("当前数字人说话状态 - state:", isDigitalHumanSpeaking, "ref:", currentIsDigitalHumanSpeaking);
      console.log("当前渲染状态:", isRenderingStarted);
      console.log("当前视频声音状态:", currentVideoSoundEnabled);

      // 检查是否是打断关键词
      const isInterruption = isInterruptionPhrase(userInputText);

      // 如果数字人正在说话（驱动中）或视频正在播放
      if (currentIsDigitalHumanSpeaking || isRenderingStarted || currentVideoSoundEnabled) {
        const inputSource = fromSpeech ? '语音输入' : '文字输入';
        console.log(`数字人正在驱动或视频正在播放，检查${inputSource}是否为打断关键词:`, {
          userInput: userInputText,
          inputSource,
          isDigitalHumanSpeaking: currentIsDigitalHumanSpeaking,
          isRenderingStarted,
          videoSoundEnabled: currentVideoSoundEnabled,
          isInterruption
        });

        if (isInterruption) {
          console.log(`检测到${inputSource}打断关键词，执行打断操作`);

          // 只有打断关键词才添加到聊天框
          setMessages(prev => [...prev, {
            type: 'user',
            content: userInputText,
            timestamp: Date.now()
          }]);

          // 如果数字人正在说话，打断数字人
          if (currentIsDigitalHumanSpeaking || isRenderingStarted) {
            console.log('打断数字人说话');
            handleInterrupt();
            sendWelcomeMessage();
          }

          // 如果视频声音已启用，关闭它
          if (currentVideoSoundEnabled) {
            console.log('停止视频播放');
            setVideoSoundEnabled(false);

            // 如果没有数字人在说话，直接发送欢迎语
            if (!currentIsDigitalHumanSpeaking && !isRenderingStarted) {
              sendWelcomeMessage();
            }

            // 如果轮播功能已启用，立即切换到轮播展示
            if (currentConfig.carouselEnabled) {
              console.log('视频被打断，立即切换到轮播展示');
              // 重置关键词触发状态
              setKeywordTriggered(false);

              // 如果轮播被暂停，则恢复轮播
              if (currentConfig.carouselPauseOnKeyword && carouselPaused) {
                resumeCarousel();
              } else if (!carouselActive) {
                // 如果轮播未激活，启动轮播
                startCarousel();
              } else {
                // 如果轮播已激活，立即切换到下一个轮播项
                const items = carouselItemsRef.current;
                if (items.length > 0) {
                  const nextIndex = (carouselCurrentIndex + 1) % items.length;
                  setCarouselCurrentIndex(nextIndex);
                  showCarouselItem(nextIndex);
                  console.log('立即切换到轮播项:', nextIndex);
                }
              }
            }
          }

          setIsProcessing(false);
          return;
        } else {
          // 非打断关键词，完全忽略，不做任何回复
          const inputSource = fromSpeech ? '语音输入' : '文字输入';
          console.log(`数字人正在驱动或视频正在播放，${inputSource}非打断关键词，完全忽略:`, userInputText);

          setIsProcessing(false);
          return;
        }
      }

      // ===== 数字人未驱动且视频未播放时的后续逻辑 =====

      // 如果是语音输入，需要处理唤醒词逻辑
      if (fromSpeech && currentConfig.voiceWakeupEnabled) {
        console.log('语音唤醒已启用，当前唤醒状态:', {
          isWakeupActive, // React状态
          wakeupRefIsActive: wakeupStateRef.current.isActive // 引用状态
        });

        // 如果唤醒状态未激活，检查是否是唤醒词
        if (!wakeupStateRef.current.isActive) {
          if (isWakeupPhrase(userInputText)) {
            console.log('检测到唤醒词，激活唤醒状态');
            updateWakeupState(true);
            setIsProcessing(false);
            return;
          } else {
            console.log('未检测到唤醒词，忽略语音输入');
            setIsProcessing(false);
            return;
          }
        } else {
          console.log('唤醒状态已激活，继续处理语音输入');
          // 更新最后活动时间，重置超时计时
          wakeupStateRef.current.lastUpdateTime = Date.now();
          console.log('更新唤醒状态最后活动时间:', new Date().toLocaleTimeString());
        }
      }

      // ===== 通过所有状态检查，开始处理正常业务逻辑 =====

      // 添加用户消息到对话历史（只有通过所有检查的输入才会被添加）
      setMessages(prev => [...prev, {
        type: 'user',
        content: userInputText,
        timestamp: Date.now()
      }]);

      try {
        // 注意：数字人驱动状态和视频播放状态的判断已经在前面处理过了
        // 这里只处理正常的业务逻辑

        // 检查是否是视频播放命令
        if (isVideoPlayCommand(userInputText)) {
          console.log('检测到视频播放命令，启用视频声音');
          setVideoSoundEnabled(true);

          // 检查是否同时匹配关键词展示
          const matchedKeyword = currentConfig.keywordBackgrounds?.find(bg =>
            bg.keyword &&
            userInputText.includes(bg.keyword) &&
            (bg.triggerType === 'userInput' || bg.triggerType === 'both')
          );

          if (matchedKeyword) {
            console.log('视频播放命令同时匹配到关键词:', matchedKeyword.keyword);

            // 设置关键词触发状态
            setKeywordTriggered(true);

            // 如果有对应的背景或视频，切换背景
            if (matchedKeyword.url) {
              console.log('准备切换背景到:', matchedKeyword.url);

              // 如果启用了轮播暂停机制，暂停轮播
              if (currentConfig.carouselPauseOnKeyword && carouselActive) {
                pauseCarousel();
              }

              // 传入keepSound=true参数，保持视频声音开启
              changeBackground(matchedKeyword.url, getResourceType(matchedKeyword.url), true);
            }

            // 如果有配置的回复，使用它而不是默认消息
            if (matchedKeyword.response && typeof matchedKeyword.response === 'string' && matchedKeyword.response.trim() !== '') {
              const response = matchedKeyword.response;
              // 添加AI回复到对话历史
              const newMessage: Message = {
                type: 'digitalHuman',
                content: response,
                timestamp: Date.now()
              };
              setMessages(prev => [...prev, newMessage]);

              // 发送回复给数字人
              dhIframe.sendMessage({
                action: 'TEXT_RENDER',
                body: response,
                requestId: commandId
              });
              setIsDigitalHumanSpeaking(true);

              setInputText('');
              setIsProcessing(false);
              return;
            }
          }

          // 如果没有匹配关键词或关键词没有配置回复，则显示默认提示
          const responseText = '已启用视频声音，说"停止"或"打断"可以停止播放';

          // 获取当前配置的打断关键词
          const interruptionKeywords = currentConfig.interruptionKeywords?.length ?
            currentConfig.interruptionKeywords.join('、') : '停止、等一下、打断';

          setDisplayContent(`视频声音已启用，请说"${interruptionKeywords}"等关键词来打断播放`);

          const newMessage: Message = {
            type: 'digitalHuman',
            content: responseText,
            timestamp: Date.now()
          };
          setMessages(prev => [...prev, newMessage]);

          // 发送确认消息给数字人
          dhIframe.sendMessage({
            action: 'TEXT_RENDER',
            body: responseText,
            requestId: commandId
          });
          setIsDigitalHumanSpeaking(true);

          setInputText('');
          setIsProcessing(false);
          return;
        }

        // 检查用户输入是否包含预设关键词
        const matchedKeyword = currentConfig.keywordBackgrounds?.find(bg =>
          bg.keyword &&
          userInputText.includes(bg.keyword) &&
          (bg.triggerType === 'userInput' || bg.triggerType === 'both')
        );

        let aiResponse = '';

        if (matchedKeyword && matchedKeyword.response) {
          console.log('匹配到用户输入关键词:', matchedKeyword.keyword);
          aiResponse = matchedKeyword.response;

          // 设置关键词触发状态
          setKeywordTriggered(true);

          // 如果有对应的背景或视频，切换背景
          if (matchedKeyword.url) {
            console.log('准备切换背景到:', matchedKeyword.url);

            // 如果启用了轮播暂停机制，暂停轮播
            if (currentConfig.carouselPauseOnKeyword && carouselActive) {
              pauseCarousel();
            }

            // 🔥 修复：用户输入关键词触发的背景切换也应该保持视频声音状态
            const shouldKeepSound = videoSoundEnabledRef.current;
            console.log('用户输入关键词触发背景切换，是否保持视频声音:', shouldKeepSound);

            changeBackground(matchedKeyword.url, getResourceType(matchedKeyword.url), shouldKeepSound);
          }

          // 添加AI回复到对话历史（非流式）
          setMessages(prev => [...prev, {
            type: 'digitalHuman',
            content: aiResponse,
            timestamp: Date.now()
          }]);

        } else {
          // 没有匹配关键词，调用OpenAI获取回复
          // 流式API会自己添加消息，不需要在这里添加
          aiResponse = await callOpenAI(userInputText);
          // 不再需要再次添加消息到历史记录，callOpenAI已经处理了
        }

        // 确保音频未静音
        if (videoIsMuted) {
          setVideoIsMuted(false);
          dhIframe.sendCommand({
            subType: 'muteAudio',
            subContent: false
          });
        }

        // 发送AI回复给数字人
        console.log('准备驱动数字人说话:', { aiResponse, commandId });
        console.log('发送前的数字人状态:', { isDigitalHumanSpeaking, isRenderingStarted });
        dhIframe.sendMessage({
          action: 'TEXT_RENDER',
          body: aiResponse,
          requestId: commandId
        });
        console.log('已发送TEXT_RENDER消息，设置isDigitalHumanSpeaking为true');
        setIsDigitalHumanSpeaking(true);

        // 对话后更新唤醒状态
        if (currentConfig.voiceWakeupEnabled) {
          updateWakeupState(true);
        }

        // 清空输入框，无论是否使用的是传入的文本
        setInputText('');
      } catch (error) {
        console.error('处理消息失败:', error);
      } finally {
        setIsProcessing(false);
      }
    }
  };

  // 处理回车发送
  const handleKeyPress = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === 'Enter') {
      handleSendMessage();
    }
  };

  useEffect(() => {
    const checkPlayUnMuteFun = async () => {
      const result = await checkPlayUnMute();
      setAudioState(prev => ({
        ...prev,
        isMuted: !result,
        isInitialized: true
      }));
      setCheckOver(true);
    };
    checkPlayUnMuteFun();
  }, []);

  useEffect(() => {
    if (realTimeVideoReady && wsConnected && checkOver && !videoIsMuted) {
      // 初始化完成
    }
  }, [realTimeVideoReady, wsConnected, checkOver, videoIsMuted]);

  useEffect(() => {
    dhIframe.registerMessageReceived(onMessage);

    // 初始化时检查一次连接状态
    const checkConnection = (msg: any) => {
      if (msg.origin === 'https://open.xiling.baidu.com') {
        const { type, content } = msg.data;
        if (type === 'msg' && content.action === 'CONNECT' && content.code === 1004) {
          alert('目前线上用户较多，请您稍后再试');
          dhIframe.removeMessageReceived(checkConnection);
        }
      }
    };

    dhIframe.registerMessageReceived(checkConnection);

    return () => {
      dhIframe.removeMessageReceived(onMessage);
      dhIframe.removeMessageReceived(checkConnection);
    };
  }, [onMessage]);

  // 添加自动滚动效果
  useEffect(() => {
    if (chatContainerRef.current) {
      chatContainerRef.current.scrollTop = chatContainerRef.current.scrollHeight;
    }
  }, [messages]);

  // 检测屏幕比例
  const getScreenRatio = () => {
    const width = window.innerWidth;
    const height = window.innerHeight;
    return width / height;
  };

  // 获取数字人 URL
  const getDigitalHumanUrl = () => {
    // 如果配置还在加载中，返回空字符串
    if (configLoading) {
      return '';
    }

    const baseUrl = 'https://open.xiling.baidu.com/cloud/realtime';
    const token = 'i-re3rdm5b3n28j/83bd16e3a561ba40bde60b1f7ec3c5b5f46d9576f29d5158e3905c06b8d306a1/2190-03-09T11:08:29.389Z';
    const initMode = 'noAudio';

    // 使用从服务器获取的配置
    const cameraId = config.outputAspectRatio === '9:16' ? '3' : config.cameraId;
    const figureId = config.figureId;

    // 添加高级参数
    const params = new URLSearchParams({
      token,
      initMode,
      cameraId: cameraId || '2', // 提供默认值以防配置加载失败
      figureId: figureId || '211862', // 提供默认值以防配置加载失败
      mode: 'inline',
      videoBg: config.isTransparent ? 'transparent' : 'solid',
      autoChromaKey: (config.autoChromaKey || true).toString(),
      ttsPitch: (config.ttsPitch || 5).toString(),
      ttsSpeed: (config.ttsSpeed || 5).toString(),
      ttsVolume: (config.ttsVolume || 5).toString(),
      ttsPer: config.ttsPer || 'CAP_4194',
      'cp-autoAnimoji': 'true',
      'cp-inactiveDisconnectSec': '180',
      'cp-preAlertSec': '10',
      showLogo: 'false',
      entry: 'false',
      'cp-showSpaceTip': 'false',
      'cp-showBaiduLogo': 'false',
      debugToolsEnabled: (config.debugToolsEnabled || true).toString()
    });

    // 添加背景图URL
    if (config.backgroundImageUrl) {
      params.append('backgroundImageUrl', config.backgroundImageUrl);
    }

    // 添加自定义参数
    if (config.customParams && Array.isArray(config.customParams)) {
      config.customParams.forEach(param => {
        if (param.key && param.value) {
          params.append(param.key, param.value);
        }
      });
    }

    return `${baseUrl}?${params.toString()}`;
  };

  // 更新 iframe 尺寸
  const updateIframeSize = () => {
    const ratio = getScreenRatio();
    const isPortrait = ratio < 1;

    if (isPortrait) {
      // 竖屏模式：使用视口宽度，高度适合屏幕
      const width = window.innerWidth;
      const height = width * (16 / 9); // 保持 16:9 的比例
      setIframeStyle({
        width: `${width}px`,
        height: `${height}px`
      });
    } else {
      // 横屏模式：使用配置中的尺寸
      if (config.previewSize) {
        setIframeStyle({
          width: `${config.previewSize.width}px`,
          height: `${config.previewSize.height}px`
        });
      }
    }
  };

  // 监听窗口大小变化
  useEffect(() => {
    // 初始化时设置一次尺寸，不再监听resize事件
    updateIframeSize();

    return () => { };
  }, [config.previewSize]);

  // 在组件加载时打印配置信息
  useEffect(() => {
    console.log('组件加载时的配置:', config);
    console.log('当前背景状态:', currentBackground);
  }, [config, currentBackground]);

  // 处理视频加载
  const handleVideoLoad = () => {
    console.log('视频加载完成');
    setVideoLoaded(true);
  };

  // 处理视频错误
  const handleVideoError = (e: any) => {
    console.error('视频加载失败:', e);
    setVideoLoaded(false);
  };

  // 在组件加载时设置默认背景类型和加载视频
  useEffect(() => {
    if (config.defaultBackgroundUrl) {
      const type = getResourceType(config.defaultBackgroundUrl);
      setCurrentBackgroundType(type);
      setCurrentBackground(config.defaultBackgroundUrl);
      console.log('设置默认背景:', config.defaultBackgroundUrl, '类型:', type);
    } else if (config.backgroundImageUrl) {
      const type = getResourceType(config.backgroundImageUrl);
      setCurrentBackgroundType(type);
      setCurrentBackground(config.backgroundImageUrl);
      console.log('设置背景图片:', config.backgroundImageUrl, '类型:', type);
    }
  }, [config.defaultBackgroundUrl, config.backgroundImageUrl]);

  // 监听视频元素加载
  useEffect(() => {
    if (videoRef.current && currentBackgroundType === 'video') {
      videoRef.current.load();
    }
  }, [currentBackground, currentBackgroundType]);

  // 清理函数
  useEffect(() => {
    return () => {
      if (wakeupStateRef.current.timeoutId) {
        clearTimeout(wakeupStateRef.current.timeoutId);
      }
      if (carouselTimerRef.current) {
        clearTimeout(carouselTimerRef.current);
      }
      if (keywordResetTimeoutRef.current) {
        clearTimeout(keywordResetTimeoutRef.current);
      }
      if (finalTimerRef.current) {
        clearTimeout(finalTimerRef.current);
      }
    };
  }, []);

  // 确保当配置变更时同步更新configRef和interruptionPhrasesRef
  useEffect(() => {
    console.log('配置已更新，同步到configRef');
    configRef.current = config;
    console.log('当前configRef中的唤醒词列表:', configRef.current.voiceWakeupPhrases);
    // 如果配置中有打断关键词，记录一下
    if (config.interruptionKeywords?.length) {
      console.log('当前打断关键词设置:', config.interruptionKeywords);
    }
  }, [config]);

  // 监听轮播配置变化
  useEffect(() => {
    const currentConfig = configRef.current;

    if (currentConfig.carouselEnabled && currentConfig.carouselItems?.length) {
      // 如果轮播未激活或配置发生变化，重启轮播
      if (!carouselActive) {
        console.log('轮播配置已启用，启动轮播');
        startCarousel();
      } else {
        // 更新轮播项目列表
        const enabledItems = currentConfig.carouselItems.filter(item => item.enabled);
        carouselItemsRef.current = enabledItems;
        console.log('轮播配置已更新，项目数量:', enabledItems.length);
      }
    } else if (carouselActive && !currentConfig.carouselEnabled) {
      // 如果轮播被禁用，停止轮播
      console.log('轮播配置已禁用，停止轮播');
      stopCarousel();
    }
  }, [config.carouselEnabled, config.carouselItems, config.carouselInterval, carouselActive, startCarousel, stopCarousel]);

  // 启动轮播项目调度
  useEffect(() => {
    if (carouselActive && !carouselPaused && carouselItemsRef.current.length > 1) {
      scheduleNextCarouselItem();
    }

    return () => {
      if (carouselTimerRef.current) {
        clearTimeout(carouselTimerRef.current);
        carouselTimerRef.current = null;
      }
    };
  }, [carouselActive, carouselPaused, carouselCurrentIndex, scheduleNextCarouselItem]);

  // 添加智能打断函数
  const intelligentInterrupt = (response?: string) => {
    console.log('执行智能打断，当前渲染状态:', {
      isRenderingStarted,
      currentRenderRequestId,
      isDigitalHumanSpeaking
    });

    // 如果数字人已经开始渲染，需要发送打断指令
    if (isRenderingStarted) {
      // 发送打断指令
      dhIframe.sendMessage({
        action: 'TEXT_RENDER',
        body: '<interrupt></interrupt>',
        requestId: commandId
      });

      // 如果有后续响应，短暂延迟后发送
      if (response) {
        setTimeout(() => {
          console.log('发送打断后的回复:', response);
          dhIframe.sendMessage({
            action: 'TEXT_RENDER',
            body: response,
            requestId: commandId
          });
          setIsDigitalHumanSpeaking(true);
        }, 300); // 短暂延迟确保打断命令先被处理
      }
    } 
    // 如果数字人尚未开始渲染但正在说话(可能是初始化阶段)，直接发送新消息
    else if (isDigitalHumanSpeaking && response) {
      dhIframe.sendMessage({
        action: 'TEXT_RENDER',
        body: response,
        requestId: commandId
      });
      setIsDigitalHumanSpeaking(true);
    }
    // 数字人未在说话，不需要打断
    else if (response) {
      dhIframe.sendMessage({
        action: 'TEXT_RENDER',
        body: response,
        requestId: commandId
      });
      setIsDigitalHumanSpeaking(true);
    }
  };

  // 打断数字人
  const handleInterrupt = () => {
    dhIframe.sendMessage({
      action: 'TEXT_RENDER',
      body: '<interrupt></interrupt>',
      requestId: commandId
    });
  };

  // 发送欢迎语
  const sendWelcomeMessage = () => {
    const welcomeMessage = "您好，请问有什么可以帮助您的";

    // 添加欢迎消息到聊天框
    setMessages(prev => [...prev, {
      type: 'digitalHuman',
      content: welcomeMessage,
      timestamp: Date.now()
    }]);

    // 短暂延迟后发送欢迎消息给数字人
    setTimeout(() => {
      console.log('发送打断后的欢迎语:', welcomeMessage);
      dhIframe.sendMessage({
        action: 'TEXT_RENDER',
        body: welcomeMessage,
        requestId: commandId
      });
      setIsDigitalHumanSpeaking(true);
    }, 300); // 短暂延迟确保打断命令先被处理
  };

  // 添加音频状态指示相关状态
  const [audioVolume, setAudioVolume] = useState<number>(0);
  const [showVolumeIndicator, setShowVolumeIndicator] = useState<boolean>(false);
  const volumeIndicatorTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  // 添加音量变化处理函数
  const handleVolumeChange = useCallback((volume: number) => {
    setAudioVolume(volume);
    setShowVolumeIndicator(true);

    // 清除之前的超时
    if (volumeIndicatorTimeoutRef.current) {
      clearTimeout(volumeIndicatorTimeoutRef.current);
    }

    // 设置新的超时，在1秒后隐藏音量指示器
    volumeIndicatorTimeoutRef.current = setTimeout(() => {
      setShowVolumeIndicator(false);
    }, 1000);
  }, []);

  // 在组件卸载时清理超时
  useEffect(() => {
    return () => {
      if (volumeIndicatorTimeoutRef.current) {
        clearTimeout(volumeIndicatorTimeoutRef.current);
      }
    };
  }, []);

  // 处理语音识别结果 - 简化为仅做语音转文字，后续逻辑复用文字输入处理
  const handleSpeechResult = (text: string) => {
    console.log('处理最终语音识别结果:', text);

    // 清空中间识别结果
    setInterimText('');

    // 如果正在处理中，忽略新的输入
    if (recognitionStateRef.current.isProcessing || isProcessing) {
      console.log('正在处理中，忽略新的语音输入');
      return;
    }

    // 设置处理中状态
    recognitionStateRef.current.isProcessing = true;

    // 语音识别仅作为语音转文字，后续所有逻辑（包括唤醒词检测、打断逻辑等）都复用文字输入的处理逻辑
    console.log('语音转文字完成，调用文字输入处理逻辑:', text);

    // 使用通用的handleSendMessage处理所有后续逻辑，并传入fromSpeech=true参数
    handleSendMessage(text, true).finally(() => {
      recognitionStateRef.current.isProcessing = false;
    });
  };

  return (
    <div className="digital-human-container" style={{
      backgroundImage: config.defaultBackgroundUrl ? `url(${config.defaultBackgroundUrl})` : 'none',
      backgroundSize: 'cover',
      backgroundPosition: 'center',
      backgroundRepeat: 'no-repeat',
      width: '100vw',
      height: '100vh',
      position: 'relative'
    }}>
      {configLoading ? (
        <div className="loading-container" style={{
          position: 'absolute',
          top: 0,
          left: 0,
          width: '100%',
          height: '100%',
          display: 'flex',
          flexDirection: 'column',
          justifyContent: 'center',
          alignItems: 'center',
          backgroundColor: 'rgba(0, 0, 0, 0.7)',
          zIndex: 10
        }}>
          <div className="spinner" style={{
            width: '50px',
            height: '50px',
            border: '5px solid rgba(255, 255, 255, 0.3)',
            borderRadius: '50%',
            borderTop: '5px solid #fff',
            animation: 'spin 1s linear infinite',
            marginBottom: '20px'
          }}></div>
          <div style={{
            color: 'white',
            fontSize: '18px',
            fontWeight: 'bold'
          }}>正在加载数字人配置...</div>
          <style>{`
            @keyframes spin {
              0% { transform: rotate(0deg); }
              100% { transform: rotate(360deg); }
            }
          `}</style>
        </div>
      ) : (
        <>
          <div className="display-box">
            <div className="display-media">
              <div
                style={{
                  position: 'absolute',
                  top: 0,
                  left: 0,
                  width: '100%',
                  height: '100%',
                  backgroundColor: 'black',
                  opacity: overlayOpacity,
                  zIndex: 1,
                  transition: 'opacity 0.5s ease-in-out'
                }}
              />
              {currentBackgroundType === 'video' ? (
                <video
                  ref={videoRef}
                  src={currentBackground}
                  style={{
                    width: '100%',
                    height: '100%',
                    objectFit: 'contain',
                    opacity: videoLoaded ? 1 : 0,
                    transition: 'opacity 0.5s ease-in-out',
                    position: 'relative',
                    zIndex: 0
                  }}
                  autoPlay
                  loop={!config.carouselWaitForVideoEnd}
                  muted={!videoSoundEnabled}
                  playsInline
                  onLoadedData={handleVideoLoad}
                  onError={handleVideoError}
                  onEnded={() => {
                    console.log('视频播放完成');
                    // 视频播放完成后重置声音状态
                    setVideoSoundEnabled(false);
                    
                    // 获取当前配置
                    const currentConfig = configRef.current;

                    // 视频播放结束后更新显示内容为唤醒提示
                    if (currentConfig.voiceWakeupEnabled && !wakeupStateRef.current.isActive) {
                      setDisplayContent(getWakeupPrompt());
                    }

                    // 先判断轮播是否启用
                    if (currentConfig.carouselEnabled) {
                      // 如果是关键词触发的视频且设置了暂停轮播，需要恢复轮播
                      if (keywordTriggered && currentConfig.carouselPauseOnKeyword) {
                        console.log('关键词触发的视频播放完成，恢复轮播');
                        setKeywordTriggered(false);
                        resumeCarousel();
                        return;
                      }

                      // 如果是普通轮播视频且设置了等待视频播放完成，切换到下一个轮播项
                      if (carouselActive && currentConfig.carouselWaitForVideoEnd) {
                        console.log('轮播视频播放完成，切换到下一个轮播项');
                        triggerNextCarouselItem();
                        return;
                      }
                    }

                    // 轮播未启用或其他情况，如果是关键词触发的视频且启用了自动重置背景，切换回默认背景
                    if (keywordTriggered && currentConfig.autoResetBackground) {
                      console.log('关键词触发的视频播放完成，轮播未启用，切换回默认背景');
                      setKeywordTriggered(false);

                      if (currentConfig.defaultBackgroundUrl) {
                        console.log('切换回默认背景:', currentConfig.defaultBackgroundUrl);
                        const resourceType = getResourceType(currentConfig.defaultBackgroundUrl);
                        changeBackground(currentConfig.defaultBackgroundUrl, resourceType);
                      }
                    }
                  }}
                />
              ) : (
                <div
                  style={{
                    width: '100%',
                    height: '100%',
                    backgroundImage: currentBackground ? `url(${currentBackground})` : 'none',
                    backgroundSize: 'cover',
                    backgroundPosition: 'center',
                    backgroundRepeat: 'no-repeat',
                    transition: 'opacity 0.5s ease-in-out',
                    position: 'relative',
                    zIndex: 0
                  }}
                />
              )}
            </div>
          </div>
          <div className="display-content">
            {displayContent}
          </div>
          
          {/* 添加语音识别状态指示器 */}
          {(recognitionState.isConnecting || recognitionState.reconnecting) && (
            <div className="recognition-status-indicator" style={{
              position: 'absolute',
              top: '10px',
              left: '10px',
              backgroundColor: 'rgba(0, 0, 0, 0.7)',
              color: 'white',
              padding: '8px 12px',
              borderRadius: '4px',
              fontSize: '14px',
              zIndex: 100,
              display: 'flex',
              alignItems: 'center'
            }}>
              <div className="status-spinner" style={{
                width: '16px',
                height: '16px',
                border: '2px solid rgba(255, 255, 255, 0.3)',
                borderRadius: '50%',
                borderTop: '2px solid #fff',
                animation: 'spin 1s linear infinite',
                marginRight: '8px'
              }}></div>
              {recognitionState.reconnecting ? 
                `正在重新连接语音识别服务 (${recognitionState.reconnectCount})...` : 
                '正在连接语音识别服务...'}
            </div>
          )}
          
          {recognitionState.hasError && !recognitionState.isConnecting && !recognitionState.reconnecting && (
            <div className="recognition-error-indicator" style={{
              position: 'absolute',
              top: '10px',
              left: '10px',
              backgroundColor: 'rgba(220, 53, 69, 0.9)',
              color: 'white',
              padding: '8px 12px',
              borderRadius: '4px',
              fontSize: '14px',
              zIndex: 100
            }}>
              语音识别错误: {recognitionState.errorMessage || '未知错误'}
            </div>
          )}
          
          {isRenderingStarted && (
            <div className="interrupt-button-container" style={{
              position: 'absolute',
              top: '20px',
              right: '20px',
              zIndex: 10
            }}>
            </div>
          )}
          <div className="chat-container" ref={chatContainerRef}>
            {messages.map((message, index) => (
              <div
                key={`msg-${message.timestamp}-${index}`}
                className={`message-row ${message.type === 'user' ? 'user-row' : 'ai-row'}`}
                style={{ display: 'flex', justifyContent: message.type === 'user' ? 'flex-end' : 'flex-start' }}
              >
                <div className={`message ${message.type}`}>
                  <div className="message-content">
                    {message.content || (message.isStreaming ? (
                      <div className="loading-dots">
                        <span></span>
                        <span></span>
                        <span></span>
                      </div>
                    ) : message.content)}
                  </div>
                </div>
              </div>
            ))}
            {isProcessing && !messages.some(msg => msg.isStreaming || (msg.type === 'digitalHuman' && msg.content === '')) && (
              <div className="message-row ai-row" style={{ display: 'flex', justifyContent: 'flex-start' }}>
                <div className="message digitalHuman">
                  <div className="message-content">
                    <div className="loading-dots">
                      <span></span>
                      <span></span>
                      <span></span>
                    </div>
                  </div>
                </div>
              </div>
            )}
          </div>
          {config.showInputField && (
            <div className="input-container">
              <input
                type="text"
                value={inputText}
                onChange={handleInputChange}
                onKeyDown={handleKeyPress}
                placeholder="请输入您想说的话..."
                className="text-input"
              />
              <button
                onClick={() => handleSendMessage()}
                className="send-button"
                disabled={!inputText.trim()}
              >
                发送
              </button>
            </div>
          )}
          <div className="mic-button-container">
            <button
              className={`mic-button ${isRecording ? 'recording' : ''} ${isProcessing ? 'processing' : ''} ${isDigitalHumanSpeaking ? 'disabled' : ''} ${!hasManuallyStarted ? 'pulse' : ''} ${recognitionState.reconnecting ? 'reconnecting' : ''}`}
              onClick={isRecording ? stopRecording : startRecording}
              disabled={isProcessing || isDigitalHumanSpeaking}
              title={!hasManuallyStarted ? '点击开启语音对话' : (isRecording ? '点击停止录音' : '点击开始录音')}
            >
              <img
                src={isRecording ? micOnIcon : micOffIcon}
                alt="麦克风"
                className="mic-icon"
              />
              
              {/* 添加音量指示器 */}
              {showVolumeIndicator && isRecording && (
                <div className="volume-indicator-container">
                  <div 
                    className="volume-indicator" 
                    style={{ 
                      height: `${Math.min(100, audioVolume)}%` 
                    }}
                  ></div>
                </div>
              )}
            </button>
            
            {/* 添加语音识别状态指示 */}
            {isRecording && (
              <div className="recognition-status">
                {isProcessing ? '处理中...' : recognitionState.reconnecting ? '重新连接中...' : '正在聆听...'}
              </div>
            )}
          </div>
          
          {/* 添加语音识别中间结果显示 */}
          {isRecording && interimText && !isProcessing && (
            <div className="interim-text-container">
              <div className="interim-text">
                正在听: {interimText}
              </div>
            </div>
          )}
          
          <div className="iframe-wrapper" style={{
            position: 'absolute',
            width: '100vw',
            height: '100vh',
            top: 0,
            left: 0,
            overflow: 'hidden',
            display: 'flex',
            justifyContent: 'center',
            alignItems: 'center',
            backgroundColor: 'transparent'
          }}>
            {!configLoading && (
              <iframe
                id="digital-human-iframe"
                src={getDigitalHumanUrl()}
                style={{
                  ...(getScreenRatio() < 1 ? iframeStyle : {}),
                  border: 'none',
                  objectFit: 'contain',
                  objectPosition: 'center center',
                  position: 'absolute',
                  top: config.previewPosition ? `${calculateActualPosition(config.previewPosition).y}px` : '50%',
                  left: config.previewPosition ? `${calculateActualPosition(config.previewPosition).x}px` : '50%',
                  transform: config.previewPosition ? 'translate(-50%, -50%)' : 'translate(-50%, -50%)'
                }}
                width={getScreenRatio() < 1 ? undefined : config.previewSize?.width || "450"}
                height={getScreenRatio() < 1 ? undefined : config.previewSize?.height || "800"}
                allow="autoplay"
              />
            )}
          </div>
        </>
      )}
      
      {/* 添加音量指示器样式 */}
      <style>{`
        .mic-button {
          position: relative;
          overflow: visible;
        }
        
        .mic-button.reconnecting {
          animation: pulse-reconnecting 1.5s infinite;
        }
        
        @keyframes pulse-reconnecting {
          0% { box-shadow: 0 0 0 0 rgba(255, 193, 7, 0.7); }
          70% { box-shadow: 0 0 0 10px rgba(255, 193, 7, 0); }
          100% { box-shadow: 0 0 0 0 rgba(255, 193, 7, 0); }
        }
        
        .volume-indicator-container {
          position: absolute;
          bottom: 100%;
          left: 50%;
          transform: translateX(-50%);
          width: 8px;
          height: 60px;
          background-color: rgba(0, 0, 0, 0.2);
          border-radius: 4px;
          margin-bottom: 10px;
        }
        
        .volume-indicator {
          position: absolute;
          bottom: 0;
          left: 0;
          width: 100%;
          background-color: #4CAF50;
          border-radius: 4px;
          transition: height 0.1s ease-out;
        }
        
        .recognition-status {
          position: absolute;
          bottom: 100%;
          left: 50%;
          transform: translateX(-50%);
          margin-bottom: 10px;
          white-space: nowrap;
          background-color: rgba(0, 0, 0, 0.7);
          color: white;
          padding: 4px 8px;
          border-radius: 4px;
          font-size: 12px;
        }
        
        .interim-text-container {
          position: fixed;
          bottom: 120px;
          left: 50%;
          transform: translateX(-50%);
          width: 80%;
          max-width: 600px;
          z-index: 100;
          text-align: center;
        }
        
        .interim-text {
          background-color: rgba(0, 0, 0, 0.7);
          color: white;
          padding: 10px 15px;
          border-radius: 8px;
          font-size: 16px;
          animation: fadeIn 0.3s ease-in-out;
          box-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
          display: inline-block;
          max-width: 100%;
          text-overflow: ellipsis;
          overflow: hidden;
        }
        
        @keyframes fadeIn {
          from { opacity: 0; transform: translateY(10px); }
          to { opacity: 1; transform: translateY(0); }
        }
      `}</style>
    </div>
  );
};

export default DigitalHuman;