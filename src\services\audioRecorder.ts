/**
 * 音频录制服务，用于捕获麦克风音频并转换为适合WebSocket传输的格式
 */
export class AudioRecorder {
  private stream: MediaStream | null = null;
  private audioContext: AudioContext | null = null;
  private processor: AudioWorkletNode | null = null;
  private analyser: AnalyserNode | null = null;
  private isRecording = false;
  private onDataAvailable: ((data: ArrayBuffer) => void) | null = null;
  private onVolumeChange: ((volume: number) => void) | null = null;
  private volumeCheckInterval: number | null = null;

  /**
   * 请求麦克风访问权限并初始化音频上下文
   */
  public async initialize(): Promise<boolean> {
    try {
      // 请求麦克风权限
      this.stream = await navigator.mediaDevices.getUserMedia({
        audio: {
          sampleRate: 16000,
          channelCount: 1,
          echoCancellation: true,
          noiseSuppression: true,
          autoGainControl: true
        },
        video: false
      });

      // 创建音频上下文
      this.audioContext = new (window.AudioContext || (window as any).webkitAudioContext)({
        sampleRate: 16000
      });

      // 创建音频分析器
      this.analyser = this.audioContext.createAnalyser();
      this.analyser.fftSize = 1024;
      this.analyser.smoothingTimeConstant = 0.8;

      // 加载音频处理工作线程
      await this.audioContext.audioWorklet.addModule('/audioProcessor.js');
      
      // 创建音频处理节点
      this.processor = new AudioWorkletNode(this.audioContext, 'audio-processor', {
        numberOfInputs: 1,
        numberOfOutputs: 1,
        processorOptions: {
          bufferSize: 4096
        }
      });

      // 设置消息处理
      this.processor.port.onmessage = (event) => {
        if (!this.isRecording || !this.onDataAvailable) return;
        
        if (event.data && event.data.audioBuffer) {
          this.onDataAvailable(event.data.audioBuffer);
        }
      };

      // 连接音频节点
      const source = this.audioContext.createMediaStreamSource(this.stream);
      source.connect(this.analyser);
      this.analyser.connect(this.processor);
      this.processor.connect(this.audioContext.destination);

      return true;
    } catch (error) {
      console.error('初始化音频录制失败:', error);
      return false;
    }
  }

  /**
   * 开始录音
   * @param onData 音频数据回调
   * @param onVolume 音量变化回调
   */
  public start(
    onData: (data: ArrayBuffer) => void,
    onVolume?: (volume: number) => void
  ): boolean {
    if (!this.audioContext || !this.processor || this.isRecording) {
      return false;
    }

    this.onDataAvailable = onData;
    this.onVolumeChange = onVolume || null;
    this.isRecording = true;

    // 通知处理器开始处理
    this.processor.port.postMessage({ command: 'start' });

    // 如果设置了音量回调，启动音量监测
    if (this.onVolumeChange && this.analyser) {
      this.startVolumeMonitoring();
    }

    return true;
  }

  /**
   * 停止录音
   */
  public stop(): void {
    this.isRecording = false;
    
    // 通知处理器停止处理
    if (this.processor) {
      this.processor.port.postMessage({ command: 'stop' });
    }
    
    this.onDataAvailable = null;
    
    // 停止音量监测
    this.stopVolumeMonitoring();
  }

  /**
   * 释放资源
   */
  public release(): void {
    this.stop();
    
    // 断开音频处理器
    if (this.processor) {
      this.processor.disconnect();
      this.processor.port.onmessage = null;
      this.processor = null;
    }
    
    // 断开分析器
    if (this.analyser) {
      this.analyser.disconnect();
      this.analyser = null;
    }
    
    // 关闭音频上下文
    if (this.audioContext && this.audioContext.state !== 'closed') {
      this.audioContext.close();
      this.audioContext = null;
    }
    
    // 关闭媒体流
    if (this.stream) {
      this.stream.getTracks().forEach(track => track.stop());
      this.stream = null;
    }
  }

  /**
   * 启动音量监测
   */
  private startVolumeMonitoring(): void {
    if (!this.analyser || !this.onVolumeChange) return;
    
    const dataArray = new Uint8Array(this.analyser.frequencyBinCount);
    
    this.volumeCheckInterval = window.setInterval(() => {
      if (!this.analyser || !this.isRecording) return;
      
      this.analyser.getByteFrequencyData(dataArray);
      
      // 计算平均音量
      let sum = 0;
      for (let i = 0; i < dataArray.length; i++) {
        sum += dataArray[i];
      }
      const average = sum / dataArray.length;
      
      // 将音量标准化到0-100范围
      const volume = Math.round((average / 255) * 100);
      
      // 调用音量变化回调
      if (this.onVolumeChange) {
        this.onVolumeChange(volume);
      }
    }, 100); // 每100毫秒检查一次
  }

  /**
   * 停止音量监测
   */
  private stopVolumeMonitoring(): void {
    if (this.volumeCheckInterval !== null) {
      clearInterval(this.volumeCheckInterval);
      this.volumeCheckInterval = null;
    }
  }

  /**
   * 检查浏览器是否支持音频录制
   */
  public static isSupported(): boolean {
    return !!(navigator.mediaDevices && 
              typeof navigator.mediaDevices.getUserMedia === 'function' && 
              (typeof window.AudioContext === 'function' || typeof (window as any).webkitAudioContext === 'function') &&
              typeof window.AudioWorklet === 'function');
  }
} 